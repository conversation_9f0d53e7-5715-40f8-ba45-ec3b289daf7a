import { Injectable, Logger } from '@nestjs/common';
import { readFileSync } from 'fs';
import { join } from 'path';
import { ShopifyOrder } from '../purchases/interfaces/shopify.interface';

interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

interface TemplateVariables {
  customerName: string;
  activationCode: string;
  orderNumber: string;
  currency: string;
  totalPrice: string;
  email: string;
}

@Injectable()
export class EmailTemplateService {
  private readonly logger = new Logger(EmailTemplateService.name);
  private readonly templatesPath = join(
    process.cwd(),
    'src',
    'mail',
    'templates',
    'activation',
  );
  private readonly sharedStylesPath = join(
    process.cwd(),
    'src',
    'mail',
    'templates',
    'shared-styles.css',
  );
  private readonly logoPath = join(
    process.cwd(),
    'src',
    'mail',
    'templates',
    'activation',
    'Logo.svg',
  );
  private translationsConfig: any;
  private sharedStyles: string;
  private logoSvg: string;

  constructor() {
    this.logger.debug(`[Constructor] Templates path: ${this.templatesPath}`);

    // Load shared styles
    try {
      this.sharedStyles = readFileSync(this.sharedStylesPath, 'utf8');
      this.logger.debug(
        `[Constructor] Loaded shared styles successfully (${this.sharedStyles.length} characters)`,
      );

      if (this.sharedStyles.length < 100) {
        this.logger.warn(
          '[Constructor] Shared styles seem too short, this might cause issues',
        );
      }
    } catch (error) {
      this.logger.error('Failed to load shared styles:', error);
      this.sharedStyles = '';
    }

    // Load logo SVG
    try {
      this.logoSvg = readFileSync(this.logoPath, 'utf8');
      this.logger.debug(
        `[Constructor] Loaded logo SVG successfully (${this.logoSvg.length} characters)`,
      );
    } catch (error) {
      this.logger.error('Failed to load logo SVG:', error);
      this.logoSvg = '';
    }

    try {
      const translationsPath = join(this.templatesPath, 'translations.json');
      this.logger.debug(
        `[Constructor] Translations config path: ${translationsPath}`,
      );

      this.translationsConfig = JSON.parse(
        readFileSync(translationsPath, 'utf8'),
      );

      this.logger.debug(
        `[Constructor] Loaded translations config successfully`,
      );
      this.logger.debug(
        `[Constructor] Supported languages: ${this.translationsConfig.supportedLanguages}`,
      );
    } catch (error) {
      this.logger.error('Failed to load translations config:', error);
      // Fallback configuration
      this.translationsConfig = {
        countryToLanguage: {
          SE: 'sv',
          Sweden: 'sv',
          FI: 'fi',
          Finland: 'fi',
          NO: 'no',
          Norway: 'no',
          DE: 'de',
          Germany: 'de',
          AT: 'de',
          Austria: 'de',
          CH: 'de',
          Switzerland: 'de',
          DK: 'da',
          Denmark: 'da',
          US: 'en',
          'United States': 'en',
          CA: 'en',
          Canada: 'en',
          GB: 'en',
          'United Kingdom': 'en',
          UK: 'en',
          AU: 'en',
          Australia: 'en',
          NZ: 'en',
          'New Zealand': 'en',
        },
        subjects: {
          en: 'Your Training Activation Code - IMVI',
          sv: 'Din träning aktiveringskod - IMVI',
          fi: 'Harjoittelun aktivointikoodi - IMVI',
          no: 'Din trenings aktiveringskode - IMVI',
          de: 'Ihr Training Aktivierungscode - IMVI',
          da: 'Din træning aktiveringskode - IMVI',
        },
        supportedLanguages: ['en', 'sv', 'fi', 'no', 'de', 'da'],
        defaultLanguage: 'en',
      };
    }
  }

  /**
   * Detect language based on billing address country
   */
  detectLanguageFromOrder(shopifyOrder: ShopifyOrder): string {
    const country =
      shopifyOrder.billing_address?.country ||
      shopifyOrder.shipping_address?.country;

    if (!country) {
      this.logger.warn('No country found in order, using default language');
      return this.translationsConfig.defaultLanguage;
    }

    this.logger.debug(`[Language Detection] Raw country value: "${country}"`);

    // Try exact match first
    let language = this.translationsConfig.countryToLanguage[country];
    this.logger.debug(
      `[Language Detection] Exact match for "${country}": ${
        language || 'none'
      }`,
    );

    // If no exact match, try case-insensitive match
    if (!language) {
      const countryUpper = country.toUpperCase();
      language = this.translationsConfig.countryToLanguage[countryUpper];
      this.logger.debug(
        `[Language Detection] Case-insensitive match for "${countryUpper}": ${
          language || 'none'
        }`,
      );
    }

    // If still no match, try to find by country name
    if (!language) {
      const countryName = this.getCountryName(country);
      language = this.translationsConfig.countryToLanguage[countryName];
      this.logger.debug(
        `[Language Detection] Country name match for "${countryName}": ${
          language || 'none'
        }`,
      );
    }

    const detectedLanguage =
      language || this.translationsConfig.defaultLanguage;

    this.logger.log(
      `Detected language '${detectedLanguage}' for country '${country}'`,
    );

    // Log available country mappings for debugging
    if (!language) {
      this.logger.warn(
        `[Language Detection] No mapping found for country "${country}". Available mappings:`,
        Object.keys(this.translationsConfig.countryToLanguage),
      );
    }

    return detectedLanguage;
  }

  /**
   * Get activation email template for specific language
   */
  getActivationEmailTemplate(
    language: string,
    variables: TemplateVariables,
  ): EmailTemplate {
    this.logger.debug(`[Template Loading] Requested language: ${language}`);
    this.logger.debug(
      `[Template Loading] Templates path: ${this.templatesPath}`,
    );

    // Ensure language is supported
    if (!this.translationsConfig.supportedLanguages.includes(language)) {
      this.logger.warn(
        `Language '${language}' not supported, falling back to default`,
      );
      language = this.translationsConfig.defaultLanguage;
    }

    try {
      // Load HTML template
      const htmlPath = join(this.templatesPath, `${language}.html`);
      this.logger.debug(`[Template Loading] HTML template path: ${htmlPath}`);
      let htmlTemplate = readFileSync(htmlPath, 'utf8');

      // Load text template
      const textPath = join(this.templatesPath, `${language}.txt`);
      this.logger.debug(`[Template Loading] Text template path: ${textPath}`);
      let textTemplate = readFileSync(textPath, 'utf8');

      // Inject shared styles, logo, and replace variables in templates
      htmlTemplate = this.injectSharedStyles(htmlTemplate);
      htmlTemplate = this.injectLogo(htmlTemplate);
      htmlTemplate = this.replaceVariables(htmlTemplate, variables);
      textTemplate = this.replaceVariables(textTemplate, variables);

      // Get subject
      const subject =
        this.translationsConfig.subjects[language] ||
        this.translationsConfig.subjects[
          this.translationsConfig.defaultLanguage
        ];

      return {
        subject,
        html: htmlTemplate,
        text: textTemplate,
      };
    } catch (error) {
      this.logger.error(
        `Failed to load template for language '${language}':`,
        error,
      );

      // Fallback to default language
      if (language !== this.translationsConfig.defaultLanguage) {
        this.logger.log(
          `Falling back to default language: ${this.translationsConfig.defaultLanguage}`,
        );
        return this.getActivationEmailTemplate(
          this.translationsConfig.defaultLanguage,
          variables,
        );
      }

      throw new Error(
        `Failed to load email template for language: ${language}`,
      );
    }
  }

  /**
   * Inject shared styles into HTML template
   */
  private injectSharedStyles(htmlTemplate: string): string {
    if (!this.sharedStyles) {
      return htmlTemplate;
    }

    // Replace existing <style> tags with shared styles
    const styleRegex = /<style[^>]*>[\s\S]*?<\/style>/gi;
    const sharedStylesTag = `<style>\n${this.sharedStyles}\n    </style>`;

    return htmlTemplate.replace(styleRegex, sharedStylesTag);
  }

  /**
   * Inject logo SVG into HTML template
   */
  private injectLogo(htmlTemplate: string): string {
    if (!this.logoSvg) {
      return htmlTemplate;
    }

    // Replace the logo placeholder with the actual SVG
    const logoPlaceholder = '<div class="logo">IMVI</div>';
    const logoSvgWithClass = this.logoSvg.replace(
      '<svg',
      '<svg class="logo-svg"',
    );

    return htmlTemplate.replace(logoPlaceholder, logoSvgWithClass);
  }

  /**
   * Replace template variables with actual values
   */
  private replaceVariables(
    template: string,
    variables: TemplateVariables,
  ): string {
    let result = template;

    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`;
      result = result.replace(new RegExp(placeholder, 'g'), value || '');
    });

    return result;
  }

  /**
   * Convert country code to country name (basic implementation)
   */
  private getCountryName(countryCode: string): string {
    const countryNames: { [key: string]: string } = {
      SE: 'Sweden',
      FI: 'Finland',
      NO: 'Norway',
      DE: 'Germany',
      DK: 'Denmark',
    };

    return countryNames[countryCode.toUpperCase()] || countryCode;
  }

  /**
   * Get supported languages
   */
  getSupportedLanguages(): string[] {
    return this.translationsConfig.supportedLanguages;
  }

  /**
   * Get default language
   */
  getDefaultLanguage(): string {
    return this.translationsConfig.defaultLanguage;
  }
}
